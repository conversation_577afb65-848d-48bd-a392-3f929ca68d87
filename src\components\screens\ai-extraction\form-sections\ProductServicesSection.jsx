import React, { useCallback, useMemo, useRef } from "react";
import <PERSON><PERSON>ield from "./components/AiField";
import {
  checkSameState,
  getFieldAlertObject,
  isSuggestion,
  recommendedOptionsMapper,
} from "../../../../components/utils/aiUtils";
import { Plus, Trash2 } from "lucide-react";

import ModalDropdown from "./components/ModalDropdown";
import { resturls } from "../../../utils/apiurls";
import Checkbox from "../../../ui-components/fields/Checkbox";
import useRealTimeValidation from "./hook/useRealTimeValidation";
import { NumericFormat } from "react-number-format";
import { formatIndianCurrency } from "../../../utils/dateUtils";
import { useParams } from "react-router-dom";
import { isEqual } from "lodash";
import AddItemForm from "./components/AddItemForm";

const SECTION = "sales_of_product_services";

const DEFAULT_ITEM_OBJ = {
  item_no: "",
  item_name: "",
  product_service_description: "",
  ledger: "",
  hsn_sac: "",
  quantity: "",
  unit: "",
  rate: "",
  amount: "",
  gst_rate_cgst: "",
  gst_rate_sgst: "",
  gst_rate_igst: "",
  gst_rate_cess: "",
  item_discount: "",
};

function ProductServicesSection({
  formData,
  setFormData,
  isReadOnly,
  invoiceType,
  formAction,
}) {
  const { businessId } = useParams();
  const billToDetails = formData["bill_to_details"] || {};
  const invoiceSummary = formData["invoice_summary"] || {};
  const data = formData[SECTION] || [];
  const lastValidated = useRef({});
  const purchaseLedgerUrl = `${resturls.getPurchaseLedger}/search?business_id=${businessId}&ledger_type=${invoiceType}`;
  const purchaseLedgerMode = useMemo(
    () => formData?.purchase_ledger_mode?.toLowerCase(),
    []
  );

  const isSameState = useMemo(() => checkSameState(formData), [formData]);

  const suggestions = useMemo(() => {
    if (!Array.isArray(data)) return [];
    return data.map((item) => {
      const recommendedField = item?.recommended_fields?.[0] ?? {};
      const isSimilarityScore = "similarity_score" in recommendedField;
      return !isSimilarityScore ? recommendedField : {};
    });
  }, [formData]);

  const [handleValidate] = useRealTimeValidation({
    data,
    formAction,
  });

  const handleChange = useCallback(
    (field, value, index) => {
      let updatedItems = [...data];
      updatedItems[index][field] = value;
      setFormData((prevData) => ({
        ...prevData,
        sales_of_product_services: updatedItems,
      }));
      return updatedItems;
    },
    [formData]
  );

  const handleRemove = useCallback((index) => {
    setFormData((prev) => {
      const prevItems = prev[SECTION] || [];
      const updatedItems = prevItems.filter(
        (_, filterIndex) => filterIndex !== index
      );
      const mergedData = {
        sales_of_product_services: updatedItems,
        bill_to_details: prev.bill_to_details,
      };
      handleValidate(SECTION, invoiceType, mergedData, true);
      const mergedDataGst = {
        sales_of_product_services: updatedItems,
        gst_ledgers: prev.gst_ledgers,
      };

      const mergedDataInvoiceSummary = {
        sales_of_product_services: updatedItems,
        gst_ledgers: prev?.gst_ledgers,
        invoice_summary: prev?.invoice_summary,
      };
      handleValidate("gst_ledgers", invoiceType, mergedDataGst, true);
      handleValidate(
        "invoice_summary",
        invoiceType,
        mergedDataInvoiceSummary,
        true
      );
      return {
        ...prev,
        [SECTION]: updatedItems,
      };
    });
  }, []);

  const handleAdd = useCallback(() => {
    if (!isReadOnly) {
      const newIndex = data?.length;
      setFormData((prev) => {
        const prevItems = prev[SECTION] || [];
        return {
          ...prev,
          [SECTION]: [...prevItems, { ...DEFAULT_ITEM_OBJ, item_no: newIndex }],
        };
      });
    }
  }, [formData, isReadOnly]);

  const handleOnPaste = useCallback((copyText, field, index) => {
    setFormData((prevData) => {
      const updatedItems = prevData.sales_of_product_services.map((item, i) =>
        i === index ? { ...item, [field]: copyText } : item
      );
      const mergedData = {
        sales_of_product_services: updatedItems,
        bill_to_details: prevData.bill_to_details,
      };
      handleValidate(SECTION, invoiceType, mergedData, true);
      if (
        field === "amount" ||
        field === "gst_rate_cgst" ||
        field === "gst_rate_sgst" ||
        field === "gst_rate_igst" ||
        field === "item_discount"
      ) {
        const mergedDataGst = {
          sales_of_product_services: updatedItems,
          gst_ledgers: prevData.gst_ledgers,
        };

        const mergedDataInvoiceSummary = {
          sales_of_product_services: updatedItems,
          gst_ledgers: prevData?.gst_ledgers,
          invoice_summary: prevData?.invoice_summary,
        };
        handleValidate("gst_ledgers", invoiceType, mergedDataGst, true);
        handleValidate(
          "invoice_summary",
          invoiceType,
          mergedDataInvoiceSummary,
          true
        );
      }
      return {
        ...prevData,
        [SECTION]: updatedItems,
      };
    });
  }, []);

  const handleOnBlurMultiValidate = useCallback(() => {
    if (isEqual(lastValidated.current, data)) return;
    const mergedData = {
      [SECTION]: data,
      bill_to_details: formData?.bill_to_details,
    };
    const mergedDataGst = {
      [SECTION]: data,
      gst_ledgers: formData?.gst_ledgers,
    };
    const mergedDataInvoiceSummary = {
      [SECTION]: data,
      gst_ledgers: formData?.gst_ledgers,
      invoice_summary: formData?.invoice_summary,
    };
    handleValidate(SECTION, invoiceType, mergedData, true);
    handleValidate("gst_ledgers", invoiceType, mergedDataGst, true);
    handleValidate(
      "invoice_summary",
      invoiceType,
      mergedDataInvoiceSummary,
      true
    );
    lastValidated.current = data;
  }, [formData]);

  const handleItemDropDownChange = useCallback((option, index) => {
    const latestSection = formAction(
      "INDEX_UPDATE_SECTION",
      SECTION,
      null,
      {
        item_name: option.value,
        item_id: String(option.key),
      },
      index
    );
    handleValidate(SECTION, invoiceType, latestSection);
  }, []);

  const handleOnBlur = useCallback(() => {
    const mergedData = {
      [SECTION]: data,
      bill_to_details: formData?.bill_to_details,
    };
    handleValidate(SECTION, invoiceType, mergedData, true);
  }, [formData]);

  return (
    <div className="flex flex-col gap-8">
      <AddItemForm open={true} onClose={() => {}} onSave={() => {}} />
      {/* Currency, Conversion Rate, Invoice Purchase Ledger */}
      <div className="flex gap-4">
        {/* Currency */}
        <div className="relative flex-grow">
          <AiField
            label="Currency"
            isExactMatch={invoiceSummary?.exact_match?.currency}
            alertObject={getFieldAlertObject(invoiceSummary, "currency")}
            required
            type="text"
            name="currency"
            id="currency"
            value={invoiceSummary?.currency ?? ""}
            onChange={(e) =>
              formAction(
                "FIELD_CHANGE",
                "invoice_summary",
                "currency",
                e.target.value
              )
            }
            disabled={isReadOnly}
          />
          {/* <Checkbox
            className="absolute top-0 right-0"
            disabled
            size="small"
            label="Convert Currency"
          /> */}
        </div>

        {/* Conversion Rate */}
        <AiField
          label="Conversion Rate"
          className="flex-grow"
          isExactMatch={invoiceSummary?.exact_match?.exchange_rate}
          alertObject={getFieldAlertObject(invoiceSummary, "exchange_rate")}
          required
          type="text"
          name="exchange_rate"
          id="exchange_rate"
          value={invoiceSummary?.exchange_rate ?? ""}
          onChange={(e) =>
            formAction(
              "FIELD_CHANGE",
              "invoice_summary",
              "exchange_rate",
              e.target.value
            )
          }
          disabled={isReadOnly}
        />

        {/* Invoice level Purchase Ledger */}
        {purchaseLedgerMode === "invoice" && invoiceType !== "expense" && (
          <div>
            <AiField
              label="Ledger"
              className="flex-grow"
              isExactMatch={billToDetails?.exact_match?.purchase_ledger_name}
              alertObject={getFieldAlertObject(
                billToDetails,
                "purchase_ledger_name"
              )}
              type="text"
              name="purchase_ledger_name"
              id="purchase_ledger_name"
              value={billToDetails?.purchase_ledger_name ?? ""}
              onChange={(e) =>
                formAction(
                  "FIELD_CHANGE",
                  "bill_to_details",
                  "purchase_ledger_name",
                  e.target.value
                )
              }
              readOnly
              disabled={isReadOnly}
              required
            />
            <ModalDropdown
              label="Select Ledger"
              url={purchaseLedgerUrl}
              searchParamName="ledger_name"
              onSelect={(option) =>
                formAction("UPDATE_SECTION", "bill_to_details", null, {
                  purchase_ledger_name: option.value,
                  purchase_ledger_id: String(option.key),
                })
              }
              transformOptionsObj={{
                key: "master_id",
              }}
              disabled={isReadOnly}
            />
          </div>
        )}
      </div>
      {Array.isArray(data) &&
        data?.map((item, index) => (
          <div
            key={item?.item_no}
            className={`${
              index !== data.length - 1
                ? "pb-5 border-b-[3px] border-[#D5D7DA] border-dashed"
                : ""
            }`}
          >
            {/* Item header */}
            <div className="flex items-center justify-between text-[#535862] font-bold text-lg mt-0 mb-4">
              Item {index + 1}
              <Trash2
                className={`w-5 h-5 stroke-red-600 cursor-pointer ${
                  isReadOnly
                    ? "opacity-50 cursor-not-allowed pointer-events-none"
                    : ""
                }`}
                onClick={() => handleRemove(index)}
              />
            </div>

            {/* fields */}
            <div className="form-grid">
              {/* Item Name */}
              <div>
                <AiField
                  label="Item Name"
                  onBlur={handleOnBlur}
                  isExactMatch={item?.exact_match?.item_name}
                  copyText={suggestions?.[index]?.item_name}
                  onPaste={(copyText, field) =>
                    handleOnPaste(copyText, field, index)
                  }
                  alertObject={getFieldAlertObject(item, "item_name")}
                  required
                  type="text"
                  value={item?.item_name ?? ""}
                  onChange={(e) =>
                    handleChange("item_name", e.target.value, index)
                  }
                  disabled={isReadOnly}
                  name="item_name"
                  id={`item_name_${index}`}
                />
                {!item.exact_match?.item_name &&
                item?.recommended_fields &&
                !isSuggestion(item?.recommended_fields) ? (
                  <ModalDropdown
                    label="View all Items"
                    options={recommendedOptionsMapper(item, "item_name")}
                    url={`${resturls.getStockItems}?business_id=${businessId}`}
                    onSelect={(option) =>
                      handleItemDropDownChange(option, index)
                    }
                    disabled={isReadOnly}
                    showMatchesFound={true}
                    searchParamName="name"
                    transformOptionsObj={{
                      key: "master_id",
                      label: "name",
                      value: "name",
                    }}
                  />
                ) : (
                  invoiceType !== "expense" && (
                    <ModalDropdown
                      label="View all Items"
                      url={`${resturls.getStockItems}?business_id=${businessId}`}
                      onSelect={(option) =>
                        handleItemDropDownChange(option, index)
                      }
                      disabled={isReadOnly}
                      searchParamName="name"
                      transformOptionsObj={{
                        key: "master_id",
                        label: "name",
                        value: "name",
                      }}
                    />
                  )
                )}
              </div>

              {/* Description */}
              <AiField
                label="Description"
                isExactMatch={item?.exact_match?.product_service_description}
                alertObject={getFieldAlertObject(
                  item,
                  "product_service_description"
                )}
                type="text"
                value={item?.product_service_description ?? ""}
                onChange={(e) =>
                  handleChange(
                    "product_service_description",
                    e.target.value,
                    index
                  )
                }
                disabled={isReadOnly}
                name="product_service_description"
                id={`product_service_description_${index}`}
              />

              {/* Item discount, hsn, ledger */}
              <div
                className={`${
                  purchaseLedgerMode === "item" || invoiceType === "expense"
                    ? "only-3-column"
                    : "only-2-column"
                }`}
              >
                {/* HSN/SAC */}
                <AiField
                  label="HSN / SAC"
                  copyText={suggestions[index]?.hsn_sac}
                  onPaste={(copyText, field) =>
                    handleOnPaste(copyText, field, index)
                  }
                  onBlur={handleOnBlur}
                  isExactMatch={item?.exact_match?.hsn_sac}
                  alertObject={getFieldAlertObject(item, "hsn_sac")}
                  type="text"
                  value={item?.hsn_sac ?? ""}
                  onChange={(e) =>
                    handleChange("hsn_sac", e.target.value, index)
                  }
                  disabled={isReadOnly}
                  name="hsn_sac"
                  id={`hsn_sac_${index}`}
                />

                {/* Item Discount */}
                <AiField
                  label="Item Discount"
                  alertObject={getFieldAlertObject(item, "item_discount")}
                  type="text"
                  copyText={suggestions[index]?.item_discount}
                  onPaste={(copyText, field) =>
                    handleOnPaste(copyText, field, index)
                  }
                  onBlur={handleOnBlurMultiValidate}
                  value={item?.item_discount ?? ""}
                  onChange={(e) =>
                    handleChange("item_discount", e.target.value, index)
                  }
                  disabled={isReadOnly}
                  name="item_discount"
                  id={`item_discount_${index}`}
                />

                {/* Item level Purchase Ledger */}
                {(purchaseLedgerMode === "item" ||
                  invoiceType === "expense") && (
                  <div>
                    <AiField
                      label="Ledger"
                      alertObject={getFieldAlertObject(
                        item,
                        "purchase_ledger_name"
                      )}
                      type="text"
                      name="purchase_ledger_name"
                      id={`purchase_ledger_name_${index}`}
                      value={item?.purchase_ledger_name ?? ""}
                      readOnly
                      disabled={isReadOnly}
                      required
                    />
                    <ModalDropdown
                      label="Select Ledger"
                      url={purchaseLedgerUrl}
                      searchParamName="ledger_name"
                      onSelect={(option) =>
                        formAction(
                          "INDEX_UPDATE_SECTION",
                          SECTION,
                          null,
                          {
                            purchase_ledger_name: option.value,
                            purchase_ledger_id: String(option.key),
                          },
                          index
                        )
                      }
                      transformOptionsObj={{
                        key: "master_id",
                      }}
                      disabled={isReadOnly}
                    />
                  </div>
                )}
              </div>

              {/* quantity, unit, rate, amount */}
              <div className="only-4-column">
                {/* Quantity */}
                <AiField
                  label="Quantity"
                  alertObject={getFieldAlertObject(item, "quantity")}
                  type="text"
                  copyText={suggestions[index]?.quantity}
                  onPaste={(copyText, field) =>
                    handleOnPaste(copyText, field, index)
                  }
                  onBlur={handleOnBlur}
                  value={item?.quantity ?? ""}
                  onChange={(e) =>
                    handleChange("quantity", e.target.value, index)
                  }
                  disabled={isReadOnly}
                  name="quantity"
                  id={`quantity_${index}`}
                />

                {/* Unit */}
                <AiField
                  label="Unit"
                  isExactMatch={item?.exact_match?.unit}
                  copyText={suggestions[index]?.unit}
                  onPaste={(copyText, field) =>
                    handleOnPaste(copyText, field, index)
                  }
                  alertObject={getFieldAlertObject(item, "unit")}
                  onBlur={handleOnBlur}
                  type="text"
                  value={item?.unit ?? ""}
                  onChange={(e) => handleChange("unit", e.target.value, index)}
                  disabled={isReadOnly}
                  name="unit"
                  id={`unit_${index}`}
                />

                {/* Rate */}
                <AiField
                  label="Rate (pre-tax)"
                  alertObject={getFieldAlertObject(item, "rate")}
                  isExactMatch={item?.exact_match?.rate}
                  type="text"
                  copyText={suggestions[index]?.rate}
                  onPaste={(copyText, field) =>
                    handleOnPaste(copyText, field, index)
                  }
                  onBlur={handleOnBlur}
                  value={item?.rate ?? ""}
                  onChange={(e) => handleChange("rate", e.target.value, index)}
                  disabled={isReadOnly}
                  name="rate"
                  id={`rate_${index}`}
                  elementClass="text-right"
                  alignSuggestionRight={true}
                />

                {/* Amount */}
                <AiField
                  label="Amount"
                  alertObject={getFieldAlertObject(item, "amount")}
                  required
                  copyText={formatIndianCurrency(
                    suggestions?.[index]?.amount,
                    true
                  )}
                  onPaste={(copyText, field) =>
                    handleOnPaste(
                      copyText?.replace(/,/g, "") || "",
                      field,
                      index
                    )
                  }
                  isExactMatch={item?.exact_match?.amount}
                  name="amount"
                  id={`amount_${index}`}
                  alignSuggestionRight={true}
                  disabled={isReadOnly}
                  renderCustomField={() => (
                    <NumericFormat
                      value={item?.amount ?? ""}
                      id={`amount_${index}`}
                      thousandSeparator={true}
                      thousandsGroupStyle="lakh"
                      decimalScale={2}
                      fixedDecimalScale
                      allowNegative={true}
                      className="input-field text-right"
                      disabled={isReadOnly}
                      onBlur={handleOnBlurMultiValidate}
                      onValueChange={(values) => {
                        handleChange("amount", values.value, index);
                      }}
                    />
                  )}
                />
              </div>

              {/* cgst, sgst, igst, cess */}
              <div
                className={`${isSameState ? "only-3-column" : "only-2-column"}`}
              >
                {isSameState && (
                  <>
                    {/* CGST Rate */}
                    <AiField
                      label="CGST Rate"
                      alertObject={getFieldAlertObject(item, "gst_rate_cgst")}
                      type="text"
                      copyText={suggestions?.[index]?.gst_rate_cgst}
                      onPaste={(copyText, field) =>
                        handleOnPaste(copyText, field, index)
                      }
                      onBlur={handleOnBlurMultiValidate}
                      isExactMatch={item?.exact_match?.gst_rate_cgst}
                      value={item?.gst_rate_cgst ?? ""}
                      onChange={(e) =>
                        handleChange("gst_rate_cgst", e.target.value, index)
                      }
                      disabled={isReadOnly}
                      name="gst_rate_cgst"
                      id={`gst_rate_cgst_${index}`}
                      elementClass="text-right"
                      alignSuggestionRight={true}
                    />

                    {/* SGST Rate */}
                    <AiField
                      label="SGST Rate"
                      alertObject={getFieldAlertObject(item, "gst_rate_sgst")}
                      type="text"
                      copyText={suggestions?.[index]?.gst_rate_sgst}
                      onPaste={(copyText, field) =>
                        handleOnPaste(copyText, field, index)
                      }
                      onBlur={handleOnBlurMultiValidate}
                      isExactMatch={item?.exact_match?.gst_rate_sgst}
                      value={item?.gst_rate_sgst ?? ""}
                      onChange={(e) =>
                        handleChange("gst_rate_sgst", e.target.value, index)
                      }
                      disabled={isReadOnly}
                      name="gst_rate_sgst"
                      id={`gst_rate_sgst_${index}`}
                      elementClass="text-right"
                      alignSuggestionRight={true}
                    />
                  </>
                )}

                {/* IGST Rate */}
                {!isSameState && (
                  <AiField
                    label="IGST Rate"
                    alertObject={getFieldAlertObject(item, "gst_rate_igst")}
                    type="text"
                    copyText={suggestions?.[index]?.gst_rate_igst}
                    onPaste={(copyText, field) =>
                      handleOnPaste(copyText, field, index)
                    }
                    onBlur={handleOnBlurMultiValidate}
                    isExactMatch={item?.exact_match?.gst_rate_igst}
                    value={item?.gst_rate_igst ?? ""}
                    onChange={(e) =>
                      handleChange("gst_rate_igst", e.target.value, index)
                    }
                    disabled={isReadOnly}
                    name="gst_rate_igst"
                    id={`gst_rate_igst_${index}`}
                    elementClass="text-right"
                    alignSuggestionRight={true}
                  />
                )}

                {/* CESS Rate */}
                <AiField
                  label="CESS Rate"
                  alertObject={getFieldAlertObject(item, "gst_rate_cess")}
                  type="text"
                  value={item?.gst_rate_cess ?? ""}
                  onChange={(e) =>
                    handleChange("gst_rate_cess", e.target.value, index)
                  }
                  disabled={isReadOnly}
                  name="gst_rate_cess"
                  id={`gst_rate_cess_${index}`}
                  elementClass="text-right"
                  alignSuggestionRight={true}
                />
              </div>
            </div>
          </div>
        ))}
      <div
        className={`flex items-center w-fit rounded-full text-lg gap-2 py-2 px-4 border border-[#D5D7DA] text-[#7E6607] ${
          isReadOnly ? "opacity-50 cursor-not-allowed" : "cursor-pointer"
        } select-none`}
        onClick={handleAdd}
      >
        <Plus className="w-6 h-6" />
        Add Item
      </div>
    </div>
  );
}

export default ProductServicesSection;
